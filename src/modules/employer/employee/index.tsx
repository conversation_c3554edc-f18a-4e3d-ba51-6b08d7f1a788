'use client';

import { useState } from 'react';
import { Plus, User } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CreateEmployeeModal, EmployeeListTable } from './components';

// Sample employee data
const employeeData = [
  {
    id: 1,
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Main St, New York, NY 10001',
    startDate: '2022-03-15',
    status: 'active',
    kycStatus: 'verified',
    bankStatus: 'verified',
  },
  {
    id: 2,
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '456 Oak Ave, Los Angeles, CA 90210',
    startDate: '2021-11-20',
    status: 'active',
    kycStatus: 'pending',
    bankStatus: 'verified',
  },
  {
    id: 3,
    fullName: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    address: '789 Pine Rd, Chicago, IL 60601',
    startDate: '2023-01-10',
    status: 'active',
    kycStatus: 'verified',
    bankStatus: 'pending',
  },
  {
    id: 4,
    fullName: 'David Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '321 Elm St, Houston, TX 77001',
    startDate: '2022-08-05',
    status: 'inactive',
    kycStatus: 'rejected',
    bankStatus: 'not_submitted',
  },
  {
    id: 5,
    fullName: 'Emma Brown',
    email: '<EMAIL>',
    phone: '+****************',
    address: '654 Maple Dr, Phoenix, AZ 85001',
    startDate: '2023-02-28',
    status: 'active',
    kycStatus: 'verified',
    bankStatus: 'verified',
  },
  {
    id: 6,
    fullName: 'Frank Miller',
    email: '<EMAIL>',
    phone: '+****************',
    address: '987 Cedar Ln, Philadelphia, PA 19101',
    startDate: '2022-12-01',
    status: 'active',
    kycStatus: 'pending',
    bankStatus: 'verified',
  },
  {
    id: 7,
    fullName: 'Grace Lee',
    email: '<EMAIL>',
    phone: '+****************',
    address: '147 Birch St, San Antonio, TX 78201',
    startDate: '2021-09-15',
    status: 'active',
    kycStatus: 'verified',
    bankStatus: 'verified',
  },
  {
    id: 8,
    fullName: 'Henry Taylor',
    email: '<EMAIL>',
    phone: '+****************',
    address: '258 Spruce Ave, San Diego, CA 92101',
    startDate: '2023-04-12',
    status: 'active',
    kycStatus: 'verified',
    bankStatus: 'pending',
  },
];

export const EmployeeModule = () => {
  const [isCreateEmployeeModalOpen, setIsCreateEmployeeModalOpen] = useState(false);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight text-gray-900">Employee Management</h2>
          <p className="text-muted-foreground text-lg">Manage all employees in the system</p>
        </div>
        <Button
          onClick={() => setIsCreateEmployeeModalOpen(true)}
          className="bg-primary hover:bg-primary/90 h-10 px-6"
        >
          <Plus className="mr-2 h-4 w-4" />
          Add Employee
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-lg">
                <User className="text-primary h-6 w-6" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Employees</p>
                <p className="text-2xl font-bold text-gray-900">{employeeData.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                <User className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Employees</p>
                <p className="text-2xl font-bold text-gray-900">
                  {employeeData.filter((emp) => emp.status === 'active').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-100">
                <User className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">KYC Pending</p>
                <p className="text-2xl font-bold text-gray-900">
                  {employeeData.filter((emp) => emp.kycStatus === 'pending').length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Employee List */}
      <EmployeeListTable />

      <CreateEmployeeModal
        isOpen={isCreateEmployeeModalOpen}
        onClose={() => setIsCreateEmployeeModalOpen(false)}
      />
    </div>
  );
};
